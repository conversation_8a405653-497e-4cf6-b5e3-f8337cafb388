import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../routes/admin_routes.dart';
import '../../services/auth_service.dart';

class AdminAuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    // Check if AuthService is initialized
    if (!Get.isRegistered<AuthService>()) {
      Get.put(AuthService());
    }

    final authService = Get.find<AuthService>();

    // If not authenticated or not admin, redirect to admin login
    if (!authService.isAuthenticated || !authService.isAdmin) {
      return const RouteSettings(name: AdminRoutes.login);
    }

    // Check specific permissions for certain routes
    if (route != null && authService.adminUser != null) {
      if (route == AdminRoutes.users &&
          !authService.adminUser!.canManageUsers) {
        Get.snackbar(
          'Access Denied',
          'You don\'t have permission to manage users',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return const RouteSettings(name: AdminRoutes.dashboard);
      }

      if (route == AdminRoutes.wallets &&
          !authService.adminUser!.canManageWallets) {
        Get.snackbar(
          'Access Denied',
          'You don\'t have permission to manage wallets',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return const RouteSettings(name: AdminRoutes.dashboard);
      }

      if (route == AdminRoutes.admins &&
          !authService.adminUser!.canManageAdmins) {
        Get.snackbar(
          'Access Denied',
          'You don\'t have permission to manage admins',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return const RouteSettings(name: AdminRoutes.dashboard);
      }
    }

    return null; // Allow access
  }
}
